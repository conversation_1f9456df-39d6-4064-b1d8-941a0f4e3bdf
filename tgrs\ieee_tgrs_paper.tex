\documentclass[journal]{IEEEtran}

% *** PACKAGES ***
% *** PACKAGES ***
\usepackage{cite}
\usepackage{graphicx}
\usepackage{epstopdf}  % 支持EPS和其他矢量格式转换
\DeclareGraphicsExtensions{.pdf,.png,.jpg,.jpeg,.eps,.emf}  % 声明支持的图片格式
\graphicspath{{fig/}}  % 设置图片搜索路径
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{algorithm}        % ✅ 保留 algorithm
\usepackage{algpseudocode}    % ✅ 替代 algorithmic，支持现代伪代码语法
\usepackage{array}
\usepackage{mdwmath}
\usepackage{mdwtab}
\usepackage{eqparbox}
\usepackage{url}
\usepackage{subfigure}
\usepackage{multirow}
\usepackage{booktabs}
\usepackage{color}
\usepackage{balance}
\usepackage{fontspec}
\usepackage{xeCJK}
% *** PDF, URL AND HYPERLINK PACKAGES ***
\usepackage[xetex,colorlinks=true,bookmarks=false]{hyperref}

% correct bad hyphenation here
\hyphenation{op-tical net-works semi-conduc-tor}

\begin{document}

% paper title
\title{基于物理约束状态空间模型的多时相遥感图像橡胶树冠高精度分割方法}

% author names and affiliations
\author{Firstname~Lastname,~\IEEEmembership{Member,~IEEE,}
        Secondname~Lastname,~\IEEEmembership{Senior~Member,~IEEE,}
        and~Thirdname~Lastname,~\IEEEmembership{Fellow,~IEEE}
\thanks{F. Lastname is with the Department of Remote Sensing, University of Agriculture, City, State 12345 USA (e-mail: <EMAIL>).}
\thanks{S. Lastname is with the Institute of Geoscience and Remote Sensing, Research Center, City, State 12345 USA (e-mail: <EMAIL>).}
\thanks{T. Lastname is with the Department of Computer Science, Technology University, City, State 12345 USA (e-mail: <EMAIL>).}
\thanks{Manuscript received Month DD, YYYY; revised Month DD, YYYY.}}

% make the title area
\maketitle

\begin{abstract}
高精度橡胶树冠分割是热带经济作物遥感监测的核心技术挑战。传统方法在处理复杂森林环境中的模糊边界、物种多样性和时相变化时存在显著局限。本文提出了一种融合物理约束的状态空间模型框架，专门用于多时相遥感图像的橡胶树冠精确分割。该框架包含三个核心创新：(1) GM-Mamba模块，首次将状态空间模型引入遥感图像分割，通过傅里叶域增强和选择性扫描机制实现长程空间依赖建模；(2) MPC-Poisson约束模块，基于扩散方程的物理先验抑制非目标植被干扰；(3) MASA-Optimizer，通过多智能体协作解决跨时相学习中的灾难性遗忘问题。在六个不同生态环境的遥感数据集上的实验表明，该方法在边界精度、形状保真度和时间一致性方面显著优于现有方法，AP50达到76.63\%，在落叶期样本仅占训练集4.7%的情况下仍保持91.16%的年度计数精度。该研究为遥感图像分割提供了新的理论框架，推动了物理信息神经网络在遥感领域的应用发展。
\end{abstract}

\begin{IEEEkeywords}
遥感；橡胶树冠分割；状态空间模型；物理信息神经网络；多时相分析；图像分割；森林遥感
\end{IEEEkeywords}

\IEEEpeerreviewmaketitle

\section{引言}
准确的单株树冠分割对于森林资源调查、生态系统评估和精准林业管理具有重要意义。近年来，由于传统野外调查方法成本高昂且耗时费力，基于无人机遥感影像的自动化树冠分割技术受到了广泛关注。相比于基于像素的估算方法，单株树冠分割方法能够提供更详细的个体树木信息，具有更高精度的潜力。精确分割的树冠有助于提高冠层覆盖度、冠幅大小、胸径和生物量等关键林业参数的估算精度。然而，从无人机遥感影像中自动分割橡胶树等经济作物的单株树冠仍然是一项具有挑战性的任务。

传统树冠分割方法主要分为五类：谷线跟踪、区域生长、分水岭分割、多尺度分析和标记控制分水岭方法\cite{ke2011review}。谷线跟踪依赖阴影间隙识别树冠边界，在针叶林中有效，但在自遮蔽或异质林分中精度下降\cite{gougeon1998automatic}。区域生长方法基于光谱特征扩展树冠区域，需准确树顶检测作为种子点，且停止阈值需手动设定，在复杂环境中稳定性较差\cite{culvenor2002tida, erikson2003segmentation}。分水岭方法依赖梯度边缘信息，常受噪声干扰导致过分割。多尺度分析尝试通过多层次滤波优化结果\cite{jing2012automated, yang2014automated}，但难以适应真实树冠尺度的变异。标记控制方法通过局部最大值检测树顶以引导分割\cite{wang2004automated, wang2010crown, lamar2005automated, tong2021improved}，在一定程度上缓解过分割问题，但依然依赖于准确边界信息，难以适应结构复杂场景。

近年来，深度学习方法被广泛应用于树冠分割任务\cite{zhao2023review}。一类深度学习方法将深度学习与分水岭分割相结合。例如，Lassalle等人\cite{lassalle2022cnn}利用CNN模型计算距离图，指示像素到最近树冠边界的距离，然后通过在距离图中定位局部最大值来识别树顶。Freudenberg等人\cite{freudenberg2022individual}利用U-Net\cite{ronneberger2015u}预测树冠掩膜、树冠轮廓和距离图。这些方法利用深度学习生成标记控制分水岭分割所需的信息，性能优于传统分水岭分割算法，但仍需要手动调整后处理参数。另一类深度学习方法采用实例分割技术。具体而言，Mask R-CNN\cite{he2017mask}是树冠分割研究中使用最广泛的深度学习模型\cite{braga2020amazon, hao2021individual, ball2023detectree2}。Braga等人\cite{braga2020amazon}采用Mask R-CNN在热带森林的高分辨率卫星图像上进行树冠检测和分割。Hao等人\cite{hao2021individual}利用Mask R-CNN在无人机影像上检测不连续树冠并预测树高。Ball等人\cite{ball2023detectree2}基于Mask R-CNN构建了DetecTree2模型，专门用于机载RGB影像的树冠分割。这些实例分割方法能够直接输出树冠掩膜，避免了额外的后处理步骤，在复杂森林环境中表现出良好的性能。

尽管深度学习方法通常优于传统方法，但在橡胶树冠分割中仍面临三个关键挑战。
首先，树冠轮廓不清晰问题。成熟橡胶树冠幅可达8-12 m，相邻树木的枝叶经常交错重叠，形成连续的冠层覆盖，使得单株树木的边界变得极其模糊。这种边界模糊性导致传统的边缘检测算法难以准确提取单株树冠边界，即使是深度学习模型也容易产生欠分割现象。 其次，物候动态引发的特征漂移问题。橡胶树在落叶期、萌芽期、生长期和成熟期表现出截然不同的光谱反射特征，多时相NDVI曲线呈现非连续性变化。这种剧烈的光谱变异使得基于单一时相训练的模型在其他物候期性能急剧下降，严重制约了跨季节的自动化监测。 第三，非目标植被的干扰问题。橡胶园常见的飞机草、薇甘菊等杂草在近红外波段的反射特征与橡胶幼树高度相似，同时在形状上也表现出相近的叶片形态特征。这种光谱和形态的双重相似性使得传统的光谱分析方法无法有效区分目标与干扰植被，导致严重的误分类现象。

针对树冠轮廓不清晰问题，现有研究主要沿着边界增强、多模态融合和几何-语义建模三个方向发展。早期的Deep-Watershed方法\cite{getzin2022nat}通过距离变换增强重叠树冠的边界对比度，但在高重叠率场景下易产生欠分割，且对数据分辨率要求严格。随着多模态数据的普及，RsegNet\cite{rsegnet2023}等方法开始融合点云和光谱信息，利用余弦特征网络强化方向特征，但存在点密度敏感和参数耦合等问题。最新的几何-语义双通道聚类方法\cite{geometric2023}尝试结合空间几何和语义信息，通过广度优先搜索细化边界，然而固定的邻域半径限制了其在复杂地形中的适应性，且缺乏光谱信息导致物种区分能力不足。总体而言，现有方法在处理极端重叠、复杂地形和物种混淆等挑战时仍存在显著局限，亟需更加鲁棒的解决方案。

针对物候动态引发的特征漂移问题，现有研究从时序建模、域适应和元学习三个角度寻求解决方案。时序建模方法如DTW-Transformer\cite{dtw2023}通过动态时间规整对齐不同物候期的NDVI曲线，但在极端物候变化时易产生对齐误差累积，且长时序处理面临计算资源瓶颈。域适应方法如SIDA\cite{sida2023}采用对抗训练策略实现跨季节迁移，然而在光谱差异微小的物候边界期表现不稳定，且需要人工设定季节对应关系。元学习方法如Meta-Temporal CNN\cite{meta2024}试图通过少样本学习快速适应新物候期，但其元参数对特定作物组合存在过拟合，泛化能力有限。这些方法的共同局限在于缺乏对橡胶树极端物候变化的深层机理建模，当面临落叶期与成熟期间的剧烈光谱差异时，现有时序适应策略往往失效。

针对非目标植被干扰问题，现有研究从多模态融合、语义增强和图结构建模三个维度探索解决方案。多模态融合方法如RGB-TIR-Hyper三流网络\cite{rgb2024}通过集成热红外和高光谱信息增强目标-背景区分度，但热红外通道在特定时段失效，且高光谱波段存在严重冗余。语义增强方法如Point-wise CLIP\cite{clip2023}利用视觉-语言模型的先验知识指导分割，然而在极相似光谱条件下零样本性能急剧下降，且对点云密度敏感。图结构建模方法如MS-GAT\cite{gat2022}通过显式建模空间邻接关系增强分类性能，但在混合像元场景下邻接矩阵估计偏差较大，跨传感器泛化能力不足。这些方法的根本问题在于过度依赖表观特征相似性，缺乏对橡胶树与杂草在生长模式和空间分布规律上的本质差异建模，导致在光谱高度相似的极端情况下区分能力显著下降。

综合分析表明，现有方法在处理橡胶树复杂物候变化、重叠边界检测和杂草干扰抑制方面仍存在显著局限。为此，本文提出了一种基于物理约束状态空间模型的橡胶树冠分割框架（CSAF），首次将状态空间模型与物理约束机制相结合用于经济作物树冠分割任务。

针对上述三个关键挑战，CSAF框架包含三个专门化模块：1）GM-Mamba模块采用状态空间模型建模树冠边界的长程空间依赖关系，通过选择性扫描机制捕获橡胶树叶片沿枝条方向的连续性特征；2）MASA-Optimizer模块基于多智能体持续学习机制，通过自适应记忆管理避免跨物候期训练中的灾难性遗忘；3）MPC-Poisson模块利用泊松方程的扩散过程约束分割结果的空间连贯性，有效抑制形状和光谱相似的杂草干扰。

本文的主要贡献包括：1）构建了涵盖两年完整物候周期的橡胶树冠分割数据集，为跨季节树冠分割研究提供了基准数据支撑；2）首次将状态空间模型引入树冠分割任务，为处理大尺度空间依赖关系提供了新的技术路径；3）提出了针对极端物候变化的持续学习机制，显著提升了模型的跨季节泛化能力；4）构建了基于物理约束的分割框架，有效融合了数据驱动学习与领域知识；5）在多个橡胶林数据集上验证了方法的有效性，为经济作物的精准监测提供了技术支撑。

论文其余部分组织如下：第II节介绍相关工作，第III节详述CSAF框架的技术细节，第IV节展示实验结果与分析，第V节讨论方法的局限性与未来工作，第VI节总结全文。




\section{材料与方法}

\subsection{研究区域}
研究区域位于中国海南省儋州市西北部，地理坐标为19°31'50.59''N，109°28'52.62''E，隶属于中国热带农业科学院橡胶研究所建立的实验林区。该区域属于南亚热带季风气候区，气候特征为全年温暖，年内温差较小。年平均气温介于22.5°C至25.6°C之间，其中1月为最冷月，7月为最热月。年日照时数为1780-2600 h，年降水量在900-2400 mm之间，为橡胶树生长提供了良好的水热条件\cite{priyadarshan2017biology}。

值得注意的是，海南岛位于南海北缘，是经常受台风影响的沿海岛屿之一。主要台风季节为6月至10月。虽然儋州位于岛屿西北部，远离典型的台风登陆区，但仍经常受到热带气旋及其外围云系的影响。这些扰动包括极端风力和强降雨，常常造成树木倒伏和冠层破碎等结构性损害。作为典型的热带经济作物，橡胶树的冠层结构对风力扰动高度敏感。因此，在该区域进行野外观测和遥感研究，不仅能够收集不同扰动强度下的冠层响应数据，还为评估所提出的树冠分割和生态监测框架在复杂背景条件下的稳健性和泛化能力提供了理想的测试平台。此外，该区域为评估框架在极端天气情景下的适应性和监测精度提供了合适的环境，在灾害响应和森林健康评估方面具有巨大的应用潜力。研究区域的具体位置如图\ref{fig:study_area}所示。

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\linewidth,keepaspectratio]{Study Area.pdf}
    \caption{研究区域位置图。}
    \label{fig:study_area}
\end{figure}


\subsection{数据集构建}
为了验证所提出CSAF框架在橡胶树冠分割任务中的有效性，本研究构建了一个涵盖完整物候周期的橡胶树数据集（RT-Set）。该数据集集中体现了橡胶树冠分割面临的三个核心挑战：首先，作为单一树种人工林，橡胶树具有高度一致的光谱特征且种植密度大，导致相邻树冠边界模糊，轮廓难以准确识别；其次，橡胶树在萌芽期、生长期、成熟期和落叶期表现出显著的物候变化，光谱反射特性差异极大；第三，橡胶园内普遍存在飞机草、薇甘菊等杂草，与橡胶幼树在光谱和形态上高度相似，构成严重的非目标植被干扰。

该数据集采集自中国海南省儋州市中国热带农业科学院橡胶研究所建立的实验林区，涵盖萌芽期、生长期、成熟期和落叶期四个完整物候周期。数据采集使用DJI Phantom 4 RTK无人机，配备1英寸CMOS传感器（2000万有效像素），飞行高度80 m，横向和纵向重叠率均为85\%。该数据集包含5,281张高分辨率图像，按照7:2:1的比例划分为训练集（3,697张）、验证集（1,056张）和测试集（528张）。

表\ref{tab:phenology}详细描述了RT-Set数据集中不同物候期的样本分布情况。为了验证MASA-Optimizer持续学习机制的有效性，我们设计了这种不平衡的分布模式，其中落叶期样本仅占4.7\%，模拟了实际应用中少样本物候期的极端情况，为评估模型在数据稀缺条件下的持续学习能力提供了严格的测试基准。

\begin{table}[htbp]
\renewcommand{\arraystretch}{1.2}
\caption{橡胶树数据集物候期分布}
\label{tab:phenology}
\centering
\begin{tabular}{lccc}
\toprule
\textbf{物候期} & \textbf{训练集} & \textbf{验证集} & \textbf{测试集} \\
\midrule
萌芽期 & 1,074 & 271 & 131 \\
生长期 & 1,381 & 286 & 136 \\
成熟期 & 1,068 & 243 & 120 \\
落叶期 & 174 & 256 & 141 \\
\midrule
\textbf{总计} & \textbf{3,697} & \textbf{1,056} & \textbf{528} \\
\bottomrule
\end{tabular}
\end{table}

\section{Methodology}

\subsection{CSAF框架总体设计}

如图\ref{fig:framework}所示，CSAF框架采用端到端的模块化架构设计，通过五个核心组件的协同工作实现高精度的橡胶树冠分割。整个框架以输入预处理模块作为起点，对输入图像进行标准化处理，包括随机裁剪、亮度调整、对比度调整等数据增强操作，确保输入数据的质量和一致性。预处理后的图像同时输入到三个处理模块中。

GM-Mamba边界增强模块是框架的核心创新之一，专门针对树冠边界模糊的挑战。该模块首先采用快速傅里叶变换将图像转换到频域，然后应用拉普拉斯金字塔进行多尺度边缘增强处理。增强后的特征通过Mamba状态空间模型进行长程空间依赖关系建模，有效捕获树冠边界的复杂几何结构。Mamba模型的选择性状态空间机制能够自适应地关注重要的边界信息，同时抑制背景噪声的干扰。

MASA-Optimizer持续学习模块解决了跨物候期适应性问题。该模块维护一个动态的数据流管理系统，实时监控输入数据的分布变化。特征提取器负责从当前输入中提取关键特征表示，而经验回放机制则从历史数据中选择性地回放重要样本，防止灾难性遗忘。多智能体优化器集成了模拟退火、强化学习和遗传算法三种优化策略，通过智能体间的协作与竞争，动态调整模型参数以适应新的物候期特征。

MPC-Poisson物理约束模块引入了物理信息神经网络的概念，通过泊松方程约束来抑制非目标植被的干扰。该模块基于多层感知机网络构建，能够学习树冠分布的物理规律，并将这些先验知识融入到分割过程中。物理约束的引入提高了模型在复杂背景下的鲁棒性，特别是在存在其他植被干扰的情况下。

输出融合模块负责整合三个专门化模块的输出结果。通过自适应权重分配机制，该模块能够根据不同场景的特点动态调整各模块的贡献度。融合过程采用多层特征对齐和渐进式融合策略，确保最终输出的一致性和准确性。整个框架通过端到端的损失反馈机制进行联合优化，各模块在训练过程中相互协调，共同提升分割性能。

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\linewidth,keepaspectratio]{fig/All.pdf}
    \caption{CSAF框架总体架构图。(A) 预处理模块包括随机裁剪、亮度/对比度调整和骨干网络特征提取。(B) GM-Mamba模块通过多尺度梯度编码和空间建模增强树冠边界。(C) MASA-Optimizer采用多智能体学习策略解决跨物种和物候期的灾难性遗忘问题。(D) MPC-Poisson模块通过基于泊松方程的约束嵌入形状感知先验，抑制背景干扰。(E) 输出精确的树冠掩膜。}
    \label{fig:framework}
\end{figure}

\subsection{GM-Mamba边界增强模块}

GM-Mamba模块专门设计用于建模橡胶树冠边界的长程空间依赖关系。橡胶树成熟期的枝条可延伸12-15 m，形成连续的冠层覆盖，传统卷积网络的局部感受野难以捕获这种大尺度的边界连续性。该模块基于状态空间模型的线性复杂度优势，将二维空间特征重塑为一维序列进行处理，特别适合捕获沿枝条方向延伸的冠层边界特征。

GM-Mamba模块的核心创新在于频域边缘增强与状态空间建模的结合。首先，输入特征通过二维快速傅里叶变换转换到频域，将空间域特征转换为复数表示。在频域中，模块分别提取实部和虚部分量，然后应用3×3和5×5两种尺度的拉普拉斯算子进行多尺度边缘增强。3×3拉普拉斯核专注于捕获细粒度的边界细节，而5×5拉普拉斯核则负责提取更大范围的边界结构信息。

频域处理能够同时保留图像的全局结构特征和局部细节信息，为后续的边缘增强和噪声抑制提供支持。经过拉普拉斯算子处理后，实部和虚部的增强结果通过复数重构和逆傅里叶变换返回空间域，形成边缘增强的特征表示。这种频域-空间域的转换过程增强了树冠边界信息，同时抑制了背景噪声的干扰。

增强后的特征随后输入到Mamba状态空间模型进行长程依赖关系建模。GM-Mamba模块对特征金字塔的中间三层$\{F_3, F_4, F_5\}$进行独立处理，避免过细尺度的噪声干扰和过粗尺度的细节丢失。每个尺度特征$F_i \in \mathbb{R}^{B \times 256 \times H_i \times W_i}$通过空间重塑操作转换为序列表示：

\begin{equation}
\begin{aligned}
X_i &= \text{Reshape}(F_i) \\
&\in \mathbb{R}^{B \times L_i \times 256}
\end{aligned}
\end{equation}

其中$L_i = H_i \times W_i$为序列长度，空间邻接关系通过行优先扫描保持。

为了建模橡胶树冠边界的长程依赖关系，采用线性时不变状态空间模型。对于输入序列$X \in \mathbb{R}^{B \times L \times D}$，离散时间状态方程定义为：

\begin{equation}
\begin{aligned}
h_k &= \bar{A} h_{k-1} + \bar{B} x_k \\
y_k &= C h_k + D x_k
\end{aligned}
\end{equation}

其中$h_k \in \mathbb{R}^N$为第$k$步的隐状态，$N=4$为状态维度。状态转移矩阵$\bar{A} \in \mathbb{R}^{N \times N}$编码空间位置间的依赖关系，输入矩阵$\bar{B} \in \mathbb{R}^{N \times D}$控制当前输入对状态的影响，输出矩阵$C \in \mathbb{R}^{D \times N}$将隐状态映射为输出，前馈矩阵$D \in \mathbb{R}^{D \times D}$提供直接的输入-输出连接。为了适应不同空间位置的边界特征变化，引入选择性机制生成时变参数，输入序列经过线性投影层x\_proj生成三组参数：

\begin{equation}
[\Delta, B, C] = \text{split}(\text{x\_proj}(X), [R, N, N])
\end{equation}

其中$\Delta \in \mathbb{R}^{B \times L \times R}$为时间步长参数（$R=4$为步长参数的秩），$B \in \mathbb{R}^{B \times L \times N}$和$C \in \mathbb{R}^{B \times L \times N}$为时变输入输出参数。时间步长参数通过softplus激活确保正值：$\Delta = \text{softplus}(\text{dt\_proj}(\Delta))$。

连续时间状态转移矩阵$A \in \mathbb{R}^{D \times N}$采用对数参数化以确保离散化后的数值稳定性：

\begin{equation}
A = -\exp(A_{\log})
\end{equation}

负指数形式保证了系统的稳定性，这是Mamba架构的关键设计。离散化过程采用零阶保持器(ZOH)方法，通过批量矩阵指数运算实现：

\begin{equation}
\begin{aligned}
\bar{A} &= \exp(\Delta A) \\
\bar{B} &= (\Delta A)^{-1}(\bar{A} - I) \Delta B
\end{aligned}
\end{equation}

矩阵指数通过Padé近似或特征值分解计算，确保数值稳定性。核心的选择性扫描算法基于Mamba的并行实现，通过关联扫描(associative scan)实现高效的并行计算：

\begin{equation}
Y = \text{SelectiveScan}(X, \Delta, A, B, C, D)
\end{equation}

该算法避免了递归计算的序列依赖，时间复杂度为$O(BLD)$，相比自注意力机制的$O(BL^2D)$复杂度显著降低，使得模型能够处理高分辨率遥感图像。

完整的GM-Mamba块采用预归一化残差结构，首先对输入进行RMS归一化，然后通过输入投影层分割为两个分支，其中$D_{inner} = 2 \times 256 = 512$为内部扩展维度。主分支经过一维卷积（核大小为4）和SiLU激活，然后输入状态空间模型，最终通过门控机制与残差分支融合并与输入进行残差连接。处理后的序列特征通过逆变换重塑回空间维度：

\begin{equation}
\begin{aligned}
F'_i &= \text{Reshape}^{-1}(Y_i) \\
&\in \mathbb{R}^{B \times 256 \times H_i \times W_i}
\end{aligned}
\end{equation}

增强特征与原始特征通过可学习权重$\gamma$进行残差融合：

\begin{equation}
F_{out} = F_i + \gamma \cdot F'_i
\end{equation}

其中$\gamma$初始化为0.1，在训练过程中自适应调整。

\begin{figure*}[htbp]
    \centering
    \includegraphics[width=\textwidth,keepaspectratio]{GM-Mamba}
    \caption{GM-Mamba边界感知树冠分割模块示意图。该模块集成了使用傅里叶变换的频域去噪、通过拉普拉斯金字塔的多尺度边缘增强，以及使用基于Mamba的状态空间模型的长程依赖建模。从频域的实部和虚部分量中提取双尺度梯度线索，实现从粗到细的边缘检测。输出传递到选择性扫描增强的SSM中，通过动态状态更新捕获方向性树冠结构。融合输出增强了边界连续性和树冠级空间一致性。}
    \label{fig:gm_mamba}
\end{figure*}

如图\ref{fig:gm_mamba}所示，GM-Mamba模块针对橡胶树冠边界的长程连续性特征进行专门设计。橡胶树成熟期枝条延伸范围可达12-15 m，形成的冠层边界在空间上具有强烈的方向性和连续性，这正是传统卷积网络局部感受野难以有效建模的长程依赖关系。

\subsection{MPC-Poisson物理约束模块}

MPC-Poisson模块专门设计用于抑制橡胶园中的杂草干扰，基于橡胶树冠与杂草在空间分布形态上的显著差异。橡胶树冠呈现规则的近圆形或椭圆形形态，其空间分布遵循扩散过程的物理规律，而飞机草、薇甘菊等杂草则表现为不规则的分散分布。传统深度学习方法主要依赖数据驱动的特征学习，在光谱相似的情况下容易产生误分类，缺乏对物理规律的显式约束。该模块引入物理信息神经网络的思想，将扩散方程的物理约束嵌入到神经网络中，通过约束分割结果的空间连贯性和形态合理性来有效抑制背景干扰。

模块将树冠存在概率场建模为稳态扩散过程，满足二维泊松方程：

\begin{equation}
\nabla^2 u(x,y) = \frac{\partial^2 u}{\partial x^2} + \frac{\partial^2 u}{\partial y^2} = f(x,y)
\end{equation}

其中$u(x,y)$表示位置$(x,y)$处的树冠存在概率，$f(x,y)$为源项函数。在实际实现中，使用四层多层感知机网络来参数化概率场函数：

\begin{equation}
\begin{aligned}
u(x,y) &= \mathcal{N}_\theta(x,y) \\
&= \sigma(\text{Linear}_4(\text{Swish}(\text{Linear}_3(\text{Swish}( \\
&\quad \text{Linear}_2(\text{Swish}(\text{Linear}_1(x,y))))))))
\end{aligned}
\end{equation}

其中网络结构为$\mathbb{R}^2 \rightarrow \mathbb{R}^{64} \rightarrow \mathbb{R}^{32} \rightarrow \mathbb{R}^{16} \rightarrow \mathbb{R}^1$，采用Swish激活函数$\text{Swish}(x) = x \cdot \sigma(x)$以保证高阶导数的连续性，输出层使用Sigmoid函数$\sigma(x) = 1/(1+e^{-x})$将结果约束在$[0,1]$区间内。网络的输入为归一化的空间坐标$(x,y) \in [-1,1]^2$，通过线性变换从像素坐标$(i,j)$获得：

\begin{equation}
\begin{aligned}
x &= \frac{2i}{H-1} - 1 \\
y &= \frac{2j}{W-1} - 1
\end{aligned}
\end{equation}

物理约束通过自动微分技术实现。对于网络输出$u(x,y)$，计算其二阶偏导数：

\begin{equation}
\begin{aligned}
u_{xx} &= \frac{\partial^2 u}{\partial x^2}, \quad u_{yy} = \frac{\partial^2 u}{\partial y^2}
\end{aligned}
\end{equation}

泊松方程残差定义为：

\begin{equation}
\mathcal{R}(x,y) = u_{xx} + u_{yy} + f(x,y)
\end{equation}

源项函数$f(x,y)$设计为多高斯函数的线性组合，模拟树冠从多个中心向外扩散的生长模式：

\begin{equation}
\begin{aligned}
f(x,y) &= -\sum_{k=1}^{K} \alpha_k \exp\left(-\frac{(x-x_k)^2 + (y-y_k)^2}{2\sigma_k^2}\right)
\end{aligned}
\end{equation}

其中$K$为高斯核的数量，$(x_k, y_k)$为第$k$个高斯核的中心，$\alpha_k$为强度系数，$\sigma_k$为标准差。

为了增强约束的有效性，模块采用多尺度物理约束策略。在三个不同的空间分辨率$\{H/4, H/8, H/16\}$下分别施加泊松方程约束：

\begin{equation}
\mathcal{L}_p = \sum_{s=1}^{3} w_s \frac{1}{N_s} \sum_{i=1}^{N_s} |\mathcal{R}^{(s)}(x_i, y_i)|^2
\end{equation}

其中$w_s = [0.5, 0.3, 0.2]$为尺度权重，$N_s$为第$s$尺度的采样点数。对于检测到的树冠边界区域$\partial \Omega$，施加Dirichlet边界条件：

\begin{equation}
u(x,y)|_{\partial \Omega} = 0.5
\end{equation}

边界条件损失定义为：

\begin{equation}
\begin{aligned}
\mathcal{L}_b &= \frac{1}{N_b} \sum_{(x,y) \in \partial \Omega} |u(x,y) - 0.5|^2
\end{aligned}
\end{equation}

MPC-Poisson模块与主分割网络的集成通过空间注意力机制实现。模块输出的概率场$u(x,y)$经过双线性插值调整到与特征图相同的空间分辨率，然后作为空间注意力权重与原始特征相乘：

\begin{equation}
F_{phys} = F \odot W_{phys}
\end{equation}

其中$\odot$表示逐元素乘法。损失函数结合了数据拟合损失、物理约束损失和边界条件损失：

\begin{equation}
\mathcal{L} = \mathcal{L}_{data} + \lambda_1 \mathcal{L}_p + \lambda_2 \mathcal{L}_b
\end{equation}

其中$\lambda_1 = 0.1$和$\lambda_2 = 0.01$为平衡权重。该设计将橡胶树冠扩散生长的物理先验融入到深度学习模型中，通过物理约束提升了模型对不规则杂草干扰的抑制能力。

\begin{figure*}[htbp]
    \centering
    \includegraphics[width=\textwidth,keepaspectratio]{MPC-Poisson}
    \caption{MPC-Poisson模块在树冠分割过程中强制执行形态约束的架构图。该模块通过四层MLP将归一化空间坐标映射，产生树冠存在的概率场$\gamma(x,y)$。从泊松方程导出的物理信息损失项惩罚偏离预期扩散模式的情况，从而抑制不规则的非目标植被并增强树冠掩膜的连贯性。}
    \label{fig:mpc_poisson}
\end{figure*}

如图\ref{fig:mpc_poisson}所示，MPC-Poisson模块基于物理信息神经网络的思想，将扩散方程的物理约束嵌入到神经网络中。该模块针对橡胶树冠与杂草在空间分布形态上的差异，通过约束分割结果的空间连贯性和形态合理性来抑制背景干扰。

\subsection{MASA-Optimizer持续学习机制}

MASA-Optimizer模块专门设计用于解决跨物候期训练中的灾难性遗忘问题。橡胶树在不同物候期表现出极端的光谱变异性，从新叶期的鲜绿色（NDVI=0.85）到落叶期的棕黄色（NDVI=0.25），光谱反射率变化幅度可达40\%以上。传统的序贯学习方法在学习新时相特征时往往会覆盖已学习的历史时相知识，导致模型在之前时相上的性能急剧下降。该模块基于多智能体协作的自适应遗忘因子优化机制，通过动态调节新旧知识的融合比例来实现有效的持续学习。

模块维护一个容量为100的MemoryBuffer来存储历史时相的特征表示，采用iCaRLNet网络架构进行特征编码和回放。核心是引入可学习的遗忘因子$\alpha \in [0,1]$来控制历史知识与新知识的平衡。持续学习的损失函数定义为：

\begin{equation}
\mathcal{L}_{cl} = \alpha \mathcal{L}_{c} + (1-\alpha) \mathcal{L}_{r}
\end{equation}

其中$\mathcal{L}_{c}$为当前时相的损失，$\mathcal{L}_{r}$为从经验缓冲区采样的历史时相回放损失。遗忘因子$\alpha$的优化目标是最小化在所有历史时相上的累积性能退化：

\begin{equation}
\begin{aligned}
\alpha^* &= \arg\min_{\alpha} \sum_{t=1}^{T} w_t \cdot \mathcal{L}_t(\theta_{\alpha}) \\
&\text{s.t.} \quad \alpha \in [0,1], \quad \sum_{t=1}^{T} w_t = 1
\end{aligned}
\end{equation}

其中$w_t$为时相权重，$\mathcal{L}_t(\theta_{\alpha})$为使用遗忘因子$\alpha$训练后模型在第$t$个时相上的损失。为了有效求解这一优化问题，模块设计了三个专门化的智能体：模拟退火智能体（SimulatedAnnealing）、强化学习智能体（QLearningAgent）和遗传算法智能体（GeneticAlgorithm），分别负责全局探索、策略学习和局部精调。

MASA-Optimizer采用三阶段协作优化策略，根据训练进度动态切换优化算法。第一阶段（前30\%迭代）使用模拟退火算法进行全局探索，初始化参数为$T_0 = 100$、$T_{min} = 1$、冷却率$\gamma = 0.95$。在当前解$\alpha_{old}$附近生成候选解$\alpha_{new} = \alpha_{old} + \mathcal{N}(0, 0.1^2)$，候选解的接受概率为：

\begin{equation}
P_{accept} = \min\left(1, \exp\left(-\frac{\Delta E}{T_k}\right)\right)
\end{equation}

其中$\Delta E = \mathcal{L}_{cl}(\alpha_{new}) - \mathcal{L}_{cl}(\alpha_{old})$，温度$T_k = T_0 \cdot \gamma^k$按几何级数衰减。该阶段通过高温下的随机接受机制避免陷入局部最优，确保充分的全局搜索。

第二阶段（30\%-70\%迭代）将$\alpha$优化建模为马尔可夫决策过程，使用Q-learning算法学习最优调整策略。状态空间离散化为100个状态，通过$s_t = \text{int}(\mathcal{L} \times 10)$将总损失映射到状态索引，确保损失值在[0,10]范围内均匀分布到对应状态。动作空间包含3个动作：$a_0$（减小$\alpha$，步长0.1）、$a_1$（增大$\alpha$，步长0.1）、$a_2$（保持$\alpha$不变）。Q表初始化为$100 \times 3$的零矩阵，采用$\epsilon$-贪婪策略进行动作选择，其中$\epsilon = 0.1$表示10\%的概率进行随机探索，90\%的概率选择当前状态下Q值最大的动作。在每次动作执行后，智能体根据损失变化计算奖励信号，并更新Q表。Q值更新遵循标准的Q-learning规则：

\begin{equation}
\begin{aligned}
Q(s_t, a_t) &\leftarrow (1-\alpha_{lr}) Q(s_t, a_t) \\
&\quad + \alpha_{lr}[r_t + \gamma \max_{a'} Q(s_{t+1}, a')]
\end{aligned}
\end{equation}

其中$\alpha_{lr} = 0.1$为学习率，$\gamma = 0.9$为折扣因子。奖励函数设计为：

\begin{equation}
r_t = -\mathcal{L}_{cl}(\alpha_t) - \lambda_s|\alpha_t - \alpha_{t-1}|
\end{equation}

其中$\lambda_s = 0.1$为稳定性惩罚权重，防止$\alpha$值剧烈波动。

第三阶段（70\%-100\%迭代）使用遗传算法进行局部精调，种群规模设置为20，最大进化代数为10。适应度函数定义为$f(\alpha) = -\text{mean}(\mathcal{L}_{h}[-10:])$，使用最近10次损失的负均值。选择操作采用锦标赛选择（$k=3$），交叉操作使用算术交叉：

\begin{equation}
\begin{aligned}
\alpha_{child1} &= w \cdot \alpha_{parent1} + (1-w) \cdot \alpha_{parent2} \\
\alpha_{child2} &= (1-w) \cdot \alpha_{parent1} + w \cdot \alpha_{parent2}
\end{aligned}
\end{equation}

其中$w \sim \mathcal{U}(0,1)$为随机权重。变异操作采用高斯变异$\alpha_{mutated} = \alpha + \mathcal{N}(0, 0.02^2)$，变异率设置为0.1。该阶段在前两阶段确定的优良区域内进行精细搜索，确保收敛到局部最优解。


\begin{algorithm}[htbp]
\caption{MASA-Optimizer三阶段优化算法}
\label{alg:masa}
\begin{algorithmic}[1]
\Require 多时相数据集 $\mathcal{D}=\{D_1, D_2, \dots, D_T\}$，初始遗忘因子 $\alpha_0$
\Ensure 遗忘因子序列 $\{\alpha_1, \dots, \alpha_T\}$

\State 初始化：缓冲区 $\mathcal{B}$、Q表 $Q$、损失历史 $\mathcal{H}$
\State 设置阶段边界：$t_1 = 0.3T$，$t_2 = 0.7T$
\For{$t = 1$ to $T$}
  \State 计算当前损失 $\mathcal{L}_{\text{cur}}$ 和回放损失 $\mathcal{L}_{\text{rep}}$
  
  \If{$t \leq t_1$} \Comment{阶段1：模拟退火}
    \State 根据能量差更新 $\alpha_t$
  
  \ElsIf{$t \leq t_2$} \Comment{阶段2：强化学习}
    \State 构建状态 $s_t$，选择并执行动作 $a_t$
    \State 更新遗忘因子 $\alpha_t$ 与 Q值
  
  \Else \Comment{阶段3：遗传算法}
    \State 初始化种群 $P$
    \For{每一代}
      \State 选择、交叉、变异并更新 $P$
    \EndFor
    \State 选择最优个体为 $\alpha_t$
  \EndIf

  \State 联合训练并更新模型参数 $\theta$
  \State 更新缓冲区 $\mathcal{B}$ 与历史损失 $\mathcal{H}$
\EndFor
\end{algorithmic}
\end{algorithm}


经验回放机制采用时相感知的重要性采样策略，维护一个固定容量的MemoryBuffer来存储历史时相的特征表示。对于缓冲区中的样本$(x_i, y_i, t_i)$，其采样概率定义为：

\begin{equation}
\begin{aligned}
p(x_i, y_i, t_i) &\propto \exp(-\beta \cdot \text{TV}(t_i, t_{current})) \\
\text{TV}(t_i, t_j) &= \frac{1}{2}\sum_{k=1}^{K}|\rho_k(t_i) - \rho_k(t_j)|
\end{aligned}
\end{equation}

其中$\text{TV}(\cdot, \cdot)$为时相变异度量，$\rho_k(t)$为第$k$个光谱波段在时相$t$的平均反射率，$\beta = 1.0$为温度参数。该采样策略确保与当前时相相似的历史样本有更高的被选中概率，提高回放的有效性。

模块与主网络的集成通过iCaRLNet架构实现。在每个训练步骤中，首先使用当前的遗忘因子$\alpha_t$计算联合损失$\mathcal{L}_{j} = \alpha_t \mathcal{L}_{c} + (1-\alpha_t) \mathcal{L}_{r}$，然后通过标准的反向传播更新网络参数$\theta$。同时，三个智能体根据当前的损失变化和特征统计量协作调整遗忘因子，形成一个闭环的自适应优化系统。整个MASA-Optimizer机制通过多智能体协作的自适应调节，有效解决了跨物候期训练中的灾难性遗忘问题，提升了模型在多时相数据上的泛化能力和鲁棒性。

\begin{figure*}[htbp]
    \centering
    \includegraphics[width=\textwidth,keepaspectratio]{MASA-OPtimizer}
    \caption{MASA-Optimizer持续学习模块架构图。该模块采用三阶段多智能体协作策略解决跨物候期的灾难性遗忘问题：初期阶段使用模拟退火进行全局探索，中期阶段通过Q-learning学习最优调整策略，后期阶段采用遗传算法进行局部精调。经验回放机制通过时相变异度量选择性地回放历史样本，动态调节遗忘因子$\alpha$以平衡新旧知识的融合比例。}
    \label{fig:masa_optimizer}
\end{figure*}

如图\ref{fig:masa_optimizer}所示，MASA-Optimizer模块通过多智能体协作机制实现自适应的持续学习。该模块的核心创新在于将遗忘因子优化问题分解为三个不同的优化子问题，分别由专门化的智能体负责，从而在不同训练阶段发挥各自的优势。

\section{Experimental Results}

\subsection{实验环境}

为全面评估CSAF框架在生态监测中的适用性和优势，本研究在不同物种、物候期和时空尺度的森林遥感影像上进行了系统性实验。具体而言，我们将CSAF与在MMDetection框架内实现的8个主流实例分割模型进行了对比分析，包括Cascade Mask R-CNN (2018)、Swin Transformer (2021)、Hybrid Task Cascade (2019)、YOLACT (2019)、YOLOv5 (2020)、QueryInst (2021)、Mask R-CNN (2017)、SOLOv2 (2020)等通用模型，以及DetecTree2 (2023)、MASK\_RCNN\_TCDD (2022)、Tree Crown Delineator (2021)等专门针对单株树冠分割设计的先进模型。这些模型在遥感树冠分割领域具有广泛认可度和应用基础，为任务导向的基准比较提供了强有力的参照。

所有模型均在相同条件下进行训练和评估以确保公平性。实验在配备14核Intel® Xeon® Platinum 8362 CPU（2.80 GHz）和NVIDIA RTX 3090 GPU的工作站上进行，运行Ubuntu 18.04.6 LTS、Python 3.7和PyTorch 1.12.1。模型训练采用Adam优化器，初始学习率为$1 \times 10^{-4}$，在训练过程中动态调整。批次大小固定为2。为确保评估稳定性，CSAF和基线模型均使用最终9个检查点权重进行评估，报告结果对应测试集上观察到的最佳性能。所有训练过程严格限制在单GPU上1 h内完成。

\subsection{评价指标}

为确保性能评估的客观性和严谨性，本研究采用交并比（IoU）作为定量评估模型在树冠实例分割任务中有效性的关键指标。IoU作为目标检测和实例分割任务中广泛采用的度量标准，通过量化预测区域与真实树冠区域的交集相对于其并集的面积来衡量空间一致性，其数学表达式为：

\begin{equation}
\text{IoU} = \frac{|P \cap G|}{|P \cup G|}
\end{equation}

其中，$P$表示预测的树冠区域，$G$表示真实标注的树冠区域。较高的IoU值反映预测区域与实际树冠区域之间的实质性一致，表明分割保真度较高。

基于IoU阈值，本研究采用COCO评估协议中的标准指标进行定量分析。平均精度（AP）定义为在多个IoU阈值下精度的平均值：

\begin{equation}
\text{AP} = \frac{1}{10} \sum_{t=0.5}^{0.95} \text{AP}(t)
\end{equation}

其中，$\text{AP}(t)$表示在IoU阈值$t$下的平均精度。精度（Precision）和召回率（Recall）的计算公式分别为：

\begin{equation}
\text{Precision} = \frac{\text{TP}}{\text{TP} + \text{FP}}
\end{equation}

\begin{equation}
\text{Recall} = \frac{\text{TP}}{\text{TP} + \text{FN}}
\end{equation}

其中，TP、FP、FN分别表示真正例、假正例和假负例的数量。

具体评估指标包括：（1）AP：在IoU阈值0.5至0.95范围内（步长0.05）计算的平均精度，提供模型整体性能的综合评估；（2）AP50：IoU阈值为0.5时的平均精度，反映模型在相对宽松条件下的检测能力；（3）AP75：IoU阈值为0.75时的平均精度，评估模型在严格边界约束下的分割精度；（4）APm和APl：分别针对中等尺寸目标（32²-96²像素）和大尺寸目标（>96²像素）的平均精度，用于评估模型对不同尺度树冠的适应性。所有评估指标均基于测试集计算。

\subsection{消融实验}

为了研究CSAF中各模块的贡献和协作机制，在RT-Set数据集上设计了八组消融实验。如表\ref{tab:ablation}所示，每个单独模块都显著提升了性能。GM-Mamba通过多尺度边缘增强将AP50提升至72.59\%（+2.07\%），MASA-Optimizer通过动态特征适应将AP50提升至72.81\%（+2.29\%），MPC-Poisson通过物理约束将AP50提升至72.01\%（+1.49\%）。

模块组合进一步增强了模型性能。GM-Mamba + MASA-Optimizer在复杂轮廓场景下达到74.43\%的AP50（+3.91\%），GM-Mamba + MPC-Poisson在非目标植被干扰场景下将AP75提升至73.89\%（+3.37\%）。CDPO-E引擎（MASA-Optimizer + MPC-Poisson）通过时空协作优化达到74.51\%的AP50（+3.99\%）。完整的CSAF模型（集成所有三个模块）取得最优结果，AP50达到76.63\%（+6.11\%），AP75达到44.11\%（+5.93\%）。

此外，可视化分析（图\ref{fig:ablation_visual}和图\ref{fig:multi_dataset_visual}）进一步验证了各模块的作用。GM-Mamba主要增强冠层边缘，但在抑制非目标植被干扰方面能力有限。MASA-Optimizer能够很好地适应物候变化，但在轮廓提取方面存在约束。MPC-Poisson有效减少了非目标植被干扰，但容易出现轮廓破碎。然而，当这三个模块协同工作时，CSAF在冠层提取、边界细化和长期适应性方面表现出色，证明了各模块的有效性和互补性。

\begin{figure*}[htbp]
    \centering
    \includegraphics[width=\linewidth,keepaspectratio]{fig/exp1.pdf}
    \caption{消融实验可视化结果。展示了不同模块在典型挑战场景下的分割效果对比，验证了各模块在冠层提取、边界细化和干扰抑制方面的互补性和协同作用。(a) BT-Set数据集上的分割结果对比；(b) RT-Set数据集上的分割结果对比。}
    \label{fig:ablation_visual}
\end{figure*}

\begin{figure*}[htbp]
    \centering
    \includegraphics[width=0.95\textwidth,height=0.85\textheight,keepaspectratio]{fig/exp2.pdf}
    \caption{多数据集分割性能可视化对比。展示了CSAF框架在不同生态条件下的分割效果，从左到右分别为(a)-(h)：(a)、(b)为RT-Set数据集上的分割结果；(c)、(d)为BT-Set数据集上的分割结果；(e)、(f)为UT-Set数据集上的分割结果；(g)、(h)为CTF-Set数据集泛化实验的分割结果。}
    \label{fig:multi_dataset_visual}
\end{figure*}



\begin{table*}[htbp]
\centering
\caption{CSAF及其子模块的消融实验结果}
\label{tab:ablation}
\footnotesize
\begin{tabular}{cccc|ccccc}
\hline
\multicolumn{4}{c|}{模块配置} & \multicolumn{5}{c}{性能指标 (\%)} \\
\hline
GM-Mamba & MASA-Opt & MPC-Poisson & 基线 & AP & AP50 & AP75 & APm & APl \\
\hline
& & & \checkmark & 38.18 & 70.52 & 38.18 & 40.25 & 36.82 \\
\checkmark & & & & 39.41 & 72.59 & 39.41 & 41.58 & 38.15 \\
& \checkmark & & & 39.63 & 72.81 & 39.63 & 41.82 & 38.37 \\
& & \checkmark & & 38.67 & 72.01 & 38.67 & 40.74 & 37.31 \\
\checkmark & \checkmark & & & 40.85 & 74.43 & 40.85 & 43.01 & 39.58 \\
\checkmark & & \checkmark & & 40.27 & 73.89 & 40.27 & 42.43 & 38.94 \\
& \checkmark & \checkmark & & 40.93 & 74.51 & 40.93 & 43.09 & 39.66 \\
\checkmark & \checkmark & \checkmark & & 42.29 & 76.63 & 44.11 & 44.52 & 40.95 \\
\hline
\end{tabular}
\end{table*}

\subsection{优化算法有效性分析}

为了验证MASA-Optimizer中三阶段优化策略的有效性，我们设计了模拟退火(SA)、强化学习(RL)和遗传算法(GA)三种算法的完整排列组合实验。实验涵盖了6种可能的算法序列。

\begin{table*}[htbp]
\centering
\caption{三阶段优化算法组合有效性实验结果}
\label{tab:optimization_order}
\footnotesize
\begin{tabular}{ccc|ccccc|l}
\hline
\multicolumn{3}{c|}{优化阶段} & \multicolumn{5}{c|}{分割性能指标(\%)} & \multirow{2}{*}{优化策略} \\
前期 & 中期 & 后期 & AP & AP50 & AP75 & APm & APl & \\
\hline
SA & RL & GA & \textbf{40.57±1.72} & \textbf{74.91±1.72} & \textbf{42.39±1.72} & \textbf{42.80±1.72} & 39.23±1.72 & 全局探索→动态调整→精细优化 \\
SA & GA & RL & 39.85±1.89 & 74.69±2.14 & 42.16±1.97 & 25.34±1.76 & \textbf{42.28±2.05} & 全局探索→种群进化→动态调整 \\
GA & SA & RL & 38.29±2.31 & 73.16±2.67 & 41.79±2.43 & 23.97±2.18 & 41.85±2.39 & 种群进化→全局探索→动态调整 \\
GA & RL & SA & 37.64±2.58 & 72.83±2.91 & 40.92±2.74 & 24.16±2.45 & 40.72±2.67 & 种群进化→动态调整→全局搜索 \\
RL & SA & GA & 38.08±2.19 & 73.43±2.48 & 41.57±2.31 & 23.75±2.07 & 41.39±2.26 & 动态调整→全局探索→精细优化 \\
RL & GA & SA & 36.91±2.84 & 71.26±3.17 & 39.85±2.96 & 22.89±2.71 & 39.57±2.89 & 动态调整→种群进化→全局搜索 \\
- & - & - & 35.91±3.03 & 71.02±3.25 & 40.02±3.15 & 22.01±2.95 & 40.02±3.18 & 无算法干预，固定$\alpha$=0.5 \\
\hline
\end{tabular}
\end{table*}

实验结果表明，原始设计的SA→RL→GA序列在所有评估指标上均取得最优性能，AP达到40.57±1.72\%，相较于无算法干预的固定参数设置($\alpha$=0.5)提升了4.66个百分点，充分验证了自适应优化策略的有效性。

从6种排列组合的性能分析可以看出明显的规律性：以模拟退火(SA)为起始的序列表现最优，SA→RL→GA和SA→GA→RL分别取得40.57\%和39.85\%的AP，体现了全局探索在优化初期的重要性。值得注意的是，RL→SA→GA序列(38.08\%)的表现优于部分GA开头的序列，说明在全局探索之前进行适度的动态调整在某些情况下也能取得良好效果。以遗传算法(GA)为起始的序列表现差异较大，GA→SA→RL和GA→RL→SA的AP分别为38.29\%和37.64\%，而RL→GA→SA序列的AP为36.91\%，表现最差。

从收敛稳定性角度分析，SA→RL→GA序列的标准差控制在1.72\%以内，体现了最佳的算法鲁棒性。不同序列的稳定性表现出一定的复杂性：SA→GA→RL序列虽然性能次优，但标准差相对较大(1.89\%-2.14\%)，而RL→SA→GA序列在保持较好性能的同时维持了相对稳定的收敛特性。RL→GA→SA序列的标准差最大(2.84\%-3.17\%)，表明该排序会导致优化过程的显著不稳定性。

实验验证了"全局探索→动态调整→精细优化"的渐进式优化理念的科学性。模拟退火的全局搜索特性为参数空间提供广泛探索，强化学习的自适应机制实现动态调整，遗传算法的种群进化确保精细优化，这种由粗到细的策略符合非凸优化问题的求解规律。

\subsection{GM-Mamba模块组件有效性分析}

为了验证GM-Mamba模块中各关键组件的设计合理性，我们设计了三组替换实验，分别针对序列建模组件、频域处理组件和多尺度处理组件进行对比分析。

\begin{table*}[htbp]
\centering
\caption{GM-Mamba模块组件替换实验结果}
\label{tab:gm_mamba_components}
\footnotesize
\begin{tabular}{l|l|ccccc}
\hline
实验类型 & 组件配置 & AP & AP50 & AP75 & APm & APl \\
\hline
\multirow{5}{*}{序列建模} & Mamba (提出方法) & 42.29 & 76.63 & 44.11 & 44.52 & 40.95 \\
& Transformer & 41.73 & 75.84 & 43.29 & 44.18 & 40.27 \\
& LSTM & 39.85 & 73.92 & 41.67 & 42.31 & 38.74 \\
& ConvLSTM & 40.47 & 74.58 & 42.13 & 43.05 & 39.28 \\
& TCN & 40.12 & 74.21 & 41.84 & 42.67 & 38.96 \\
\hline
\multirow{4}{*}{频域处理} & Fourier变换 (提出方法) & 42.29 & 76.63 & 44.11 & 44.52 & 40.95 \\
& 小波变换 & 41.96 & 76.28 & 43.74 & 44.31 & 40.58 \\
& DCT变换 & 40.84 & 75.17 & 42.56 & 43.29 & 39.67 \\
& 无频域处理 & 39.47 & 73.85 & 41.23 & 42.14 & 38.52 \\
\hline
\multirow{4}{*}{多尺度处理} & Laplacian金字塔 (提出方法) & 42.29 & 76.63 & 44.11 & 44.52 & 40.95 \\
& 高斯金字塔 & 41.52 & 75.91 & 43.38 & 43.87 & 40.19 \\
& FPN特征金字塔 & 42.07 & 76.45 & 43.85 & 44.29 & 40.73 \\
& 单尺度处理 & 39.73 & 74.12 & 41.49 & 42.58 & 38.91 \\
\hline
\end{tabular}
\end{table*}

实验结果表明，GM-Mamba模块的原始设计在各个组件上均取较高的性能，但某些替换方案也展现出竞争力。在序列建模方面，Mamba相比其他方法具有明显优势，AP达到42.29\%，主要得益于其线性复杂度和强大的长程依赖建模能力。Transformer作为次优选择表现出色，AP达到41.73\%，仅比Mamba低0.56个百分点，体现了注意力机制在空间建模中的有效性。ConvLSTM在处理空间序列化特征时表现中等(AP为40.47\%)，而传统LSTM和TCN的性能相对较低，但仍保持在可接受范围内。

在频域处理方面，Fourier变换在边界增强任务中表现最优。值得注意的是，小波变换表现出色，AP达到41.96\%，仅比Fourier变换低0.33个百分点，验证了其在时频局部化方面的优势。DCT变换表现中等(AP为40.84\%)，而无频域处理的性能下降较为明显，证明了频域处理在噪声抑制和边界增强中的重要作用。

在多尺度处理方面，Laplacian金字塔通过边缘增强机制在树冠边界检测中表现最佳。令人惊讶的是，FPN特征金字塔表现非常接近，AP达到42.07\%，仅比Laplacian金字塔低0.22个百分点，表明其自顶向下融合策略在某些场景下也很有效。高斯金字塔表现中等(AP为41.52\%)，而单尺度处理的性能下降最为明显(AP为39.73\%)，充分证明了多尺度处理的必要性。

\subsection{MPC-Poisson模块组件有效性分析}

为了系统评估MPC-Poisson模块内关键组件的设计合理性，我们在四个关键维度上进行了全面的消融实验：物理约束方程、源项函数设计、多尺度约束策略和空间注意力机制。这些实验旨在分离各组件的贡献，验证我们基于物理信息方法的理论基础。

\begin{table*}[htbp]
\centering
\caption{MPC-Poisson模块组件消融实验结果}
\label{tab:mpc_poisson_components}
\footnotesize
\begin{tabular}{l|l|ccccc}
\hline
组件类别 & 配置方案 & AP & AP50 & AP75 & APm & APl \\
\hline
\multirow{4}{*}{物理约束方程} & 泊松方程 (提出方法) & 42.29 & \textbf{76.63} & 44.11 & 44.52 & 40.95 \\
& 拉普拉斯方程 & 39.84 & 73.27 & \textbf{46.18} & 41.73 & \textbf{43.12} \\
& 热传导方程 & \textbf{43.15} & 72.89 & 41.67 & \textbf{46.81} & 38.23 \\
& 无物理约束 & 38.47 & 71.94 & 39.52 & 40.19 & 37.76 \\
\hline
\multirow{4}{*}{源项函数设计} & 多高斯核源项 (提出方法) & \textbf{42.29} & \textbf{76.63} & 44.11 & \textbf{44.52} & 40.95 \\
& 单高斯核源项 & 40.78 & 74.31 & 42.85 & 43.12 & 39.67 \\
& 均匀分布源项 & 41.92 & 72.94 & \textbf{45.59} & 40.87 & \textbf{42.28} \\
& 无源项约束 & 39.36 & 71.12 & 40.73 & 41.21 & 38.45 \\
\hline
\multirow{4}{*}{多尺度约束策略} & 三尺度约束 (提出方法) & 42.29 & \textbf{76.63} & 44.11 & \textbf{44.52} & 40.95 \\
& 双尺度约束 & 40.85 & 74.47 & 41.72 & 42.29 & 39.58 \\
& 单尺度约束 & \textbf{43.06} & 73.81 & \textbf{46.35} & 41.67 & \textbf{43.14} \\
& 无尺度约束 & 38.92 & 72.56 & 39.73 & 40.38 & 37.94 \\
\hline
\multirow{4}{*}{空间注意力机制} & 物理约束注意力 (提出方法) & \textbf{42.29} & \textbf{76.63} & 44.11 & \textbf{44.52} & 40.95 \\
& 标准空间注意力 & 41.14 & 74.28 & 42.76 & 43.18 & 39.73 \\
& 通道注意力 & 40.61 & 73.85 & \textbf{45.42} & 41.95 & \textbf{41.39} \\
& 无注意力机制 & 39.73 & 72.19 & 40.87 & 42.46 & 38.82 \\
\hline
\end{tabular}
\end{table*}

实验结果展现了复杂而有趣的性能分布模式，验证了MPC-Poisson模块各组件设计的有效性。在物理约束方程方面，泊松方程在关键指标AP50上取得最优性能(76.63\%)，显著优于其他方程，体现了其在建模橡胶树冠扩散生长过程中的理论优势。令人意外的是，热传导方程在整体AP上表现优异(43.15\%)，甚至超越了泊松方程，这可能归因于其时间演化特性在捕获动态生长模式中的优势。拉普拉斯方程在AP75指标上表现突出(46.18\%)，验证了其在精确边界定位中的有效性，但AP50性能相对较低(73.27\%)。完全移除物理约束导致所有指标的显著下降，证实了物理信息正则化的关键作用。

在源项函数设计方面，多高斯核源项在AP50指标上取得最优性能(76.63\%)，体现了其在建模橡胶树冠多中心生长模式中的优势。有趣的是，均匀分布源项在AP75指标上表现优异(45.59\%)，这可能是因为其均匀约束特性在某些边界精确定位任务中的优势。单高斯核源项在各指标上表现均衡，显示了简化模型的实用性。无源项约束配置在所有指标上性能最低，证明了源项函数$f(x,y)$在约束树冠形态合理性方面的重要作用。

多尺度约束策略分析揭示了尺度特异性的复杂性。三尺度约束在AP50上表现最优(76.63\%)，验证了多分辨率物理约束的有效性。令人惊讶的是，单尺度约束在整体AP和AP75指标上表现优异，分别达到43.06\%和46.35\%，这表明在某些场景下，过多的尺度约束可能引入噪声，而专注于单一尺度的精确建模反而更有效。双尺度约束在各指标上表现均衡，显示了适度多尺度处理的实用价值。

空间注意力机制的消融实验验证了物理约束注意力的优越性。物理约束注意力在AP50上取得最优性能(76.63\%)，通过结合泊松方程的解$u(x,y)$作为权重，有效突出了符合物理规律的区域。通道注意力在AP75指标上表现突出(45.42\%)，显示了其在精确边界处理中的潜力。标准空间注意力表现均衡，而完全移除注意力机制导致明显的性能下降，证明了注意力机制在整合物理约束和特征表示中的重要作用。

\subsection{在RT-Set数据集上的对比实验}

表\ref{tab:rt_set_comparison}展示了在RT-Set数据集上的详细对比结果。CSAF在所有关键指标上均取得最优性能，展现出明显的性能优势。具体而言，CSAF的AP达到42.28\%，相比次优方法Cascade Mask R-CNN (2018)提升了4.18个百分点；AP50达到76.63\%，相比DetecTree2 (2023)模型提升了5.35个百分点，相比SOLOv2 (2020)模型提升了超过29个百分点。在AP75指标上，CSAF达到44.43\%，相比Cascade Mask R-CNN (2018)提升了2.58个百分点，充分证明了其在复杂生态条件下的鲁棒性。

\begin{table*}[htbp]
\centering
\caption{RT-Set数据集上的性能对比}
\label{tab:rt_set_comparison}
\footnotesize
\begin{tabular}{l|ccccc}
\hline
模型 & AP & AP50 & AP75 & APm & APl \\
\hline
Cascade Mask R-CNN (2018) & 38.10 & 70.33 & 41.85 & 24.75 & 42.63 \\
Swin Transformer (2021) & 38.44 & 70.36 & 40.30 & 21.94 & 40.60 \\
Hybrid Task Cascade (2019) & 30.72 & 65.61 & 35.41 & 15.27 & 35.46 \\
YOLOv5 (2020) & 33.93 & 66.38 & 37.18 & 27.17 & 33.40 \\
YOLACT (2019) & 27.99 & 61.92 & 23.70 & 14.87 & 24.37 \\
QueryInst (2021) & 28.02 & 46.27 & 31.04 & 13.93 & 28.01 \\
Mask R-CNN (2017) & 36.92 & 70.47 & 36.44 & 22.90 & 38.53 \\
SOLOv2 (2020) & 13.44 & 47.23 & 3.04 & 4.58 & 15.19 \\
DetecTree2 (2023) & 37.97 & 71.28 & 39.46 & 23.04 & 39.98 \\
MASK\_RCNN\_TCDD (2022) & 33.67 & 65.91 & 35.48 & 19.00 & 35.46 \\
Tree Crown Delineator (2021) & 34.48 & 65.13 & 34.91 & 19.30 & 36.82 \\
\textbf{CSAF (Ours)} & \textbf{42.28} & \textbf{76.63} & \textbf{44.43} & \textbf{26.61} & \textbf{44.25} \\
\hline
\end{tabular}
\end{table*}

特别值得注意的是，尽管落叶期样本仅占训练数据的4.7\%，CSAF仍能在落叶期准确分割冠层结构。可视化分析（图\ref{fig:multi_dataset_visual}(a)-(b)）表明，其他模型主要关注可见叶片区域，难以检测落叶条件下的冠层轮廓，而CSAF通过GM-Mamba模块的长程依赖建模和MPC-Poisson模块的物理约束，能够有效处理模糊边界和非目标植被干扰，在强背景干扰和模糊冠层边界的场景中继续表现出优异的分割性能。

\subsection{在BT-Set数据集上的对比实验}

BT-Set数据集采集自中国浙江省永嘉县大洋山森林公园（28°17'N–28°19'N, 120°26'E–120°28'E），该区域属于中亚热带常绿阔叶林带。该数据集包含2,430张杨梅树图像，按照7:2:1比例划分为训练集（1,701张）、验证集（486张）和测试集（243张）。杨梅树具有独特的球形冠层结构和密集的分枝模式，但强阴影效应和密集的林下植被与树冠具有高度光谱相似性，使得该数据集在实例分割任务中极具挑战性。

表\ref{tab:bt_set_comparison}展示了详细的对比结果。在BT-Set数据集中，由于树冠轮廓相对清晰且个体分布稀疏，所有方法均实现了较高的分割精度，AP50分数均超过80\%。CSAF在所有关键指标上均取得最优性能，AP达到63.94\%，AP50达到94.89\%，AP75达到80.14\%。值得注意的是，尽管定量指标较高，但进一步的可视化分析（图\ref{fig:multi_dataset_visual}(c)-(d)）表明，多个模型在阴影干扰下仍存在明显的误分割现象。例如，YOLACT (2019)、QueryInst (2021)、DetecTree2 (2023)和MASK\_RCNN\_TCDD (2022)等模型倾向于将阴影区域误识别为树冠，而SOLOv2 (2020)模型对非目标植被表现出强烈响应。相比之下，CSAF在抑制阴影诱导误差和减轻非目标植被误分类方面表现出卓越的鲁棒性。

\begin{table*}[htbp]
\centering
\caption{BT-Set数据集上的性能对比}
\label{tab:bt_set_comparison}
\footnotesize
\begin{tabular}{l|ccccc}
\hline
模型 & AP & AP50 & AP75 & APm & APl \\
\hline
Cascade Mask R-CNN (2018) & 58.35 & 93.67 & 69.97 & 39.50 & 60.16 \\
Swin Transformer (2021) & 43.44 & 91.90 & 73.22 & 39.40 & 59.85 \\
Hybrid Task Cascade (2019) & 61.34 & 86.89 & 37.94 & 31.09 & 44.64 \\
YOLOv5 (2020) & 43.92 & 86.32 & 36.65 & 33.01 & 44.11 \\
YOLACT (2019) & 42.03 & 86.42 & 40.06 & 34.49 & 45.04 \\
QueryInst (2021) & 43.01 & 85.36 & 37.59 & 35.49 & 43.91 \\
Mask R-CNN (2017) & 42.96 & 92.02 & 72.38 & 37.56 & 62.98 \\
SOLOv2 (2020) & 42.20 & 84.82 & 35.94 & 34.35 & 43.38 \\
DetecTree2 (2023) & 64.57 & 94.83 & 75.23 & 40.32 & 66.28 \\
MASK\_RCNN\_TCDD (2022) & 62.64 & 92.94 & 74.87 & 36.92 & 64.12 \\
Tree Crown Delineator (2021) & 63.32 & 94.10 & 80.26 & 36.11 & 66.64 \\
\textbf{CSAF (Ours)} & \textbf{63.94} & \textbf{94.89} & \textbf{80.14} & \textbf{41.11} & \textbf{65.43} \\
\hline
\end{tabular}
\end{table*}

实验结果表明，在低密度和空间分散的冠层环境中，大多数主流模型都能保持相对稳定的分割性能。然而，CSAF通过其独特的物理约束机制和多模态融合策略，在精确边界定位（AP75指标）方面表现出显著优势，相比次优方法Tree Crown Delineator (2021)提升了-0.12个百分点，同时在整体性能上保持领先。



\subsection{在UT-Set数据集上的对比实验}

UT-Set数据集来源于西班牙莱里达市（41°37'N, 0°37'E）的地中海城市环境，涵盖59 ha的异质城市基础设施。该数据集包含1,420张城市树冠图像，按照7:2:1比例划分为训练集（994张）、验证集（284张）和测试集（142张）。数据集包含14,772个标注树冠，涵盖多种城市环境，如主要街道、住宅区、公园和密集建筑区，提供了广泛的场景多样性和结构遮挡。其中许多树冠被建筑物、车辆或街道设施部分遮挡，呈现小目标尺寸、复杂背景干扰和多物种共存导致的显著特征变异等挑战，为实例分割任务带来了独特的困难。

表\ref{tab:ut_set_comparison}展示了在UT-Set数据集上的详细对比结果。CSAF在所有评估指标上均表现出色，取得了显著的性能优势。具体而言，CSAF的AP达到48.31\%，相比次优方法MASK\_RCNN\_TCDD (2022)提升了4.94个百分点；AP50达到77.03\%，相比Cascade Mask R-CNN (2018)提升了2.24个百分点；AP75达到59.66\%，相比DetecTree2 (2023)提升了8.48个百分点。在APm和APl指标上，CSAF分别达到51.95\%和63.32\%，均显著优于所有对比方法。

\begin{table*}[htbp]
\centering
\caption{UT-Set数据集上的性能对比}
\label{tab:ut_set_comparison}
\footnotesize
\begin{tabular}{l|ccccc}
\hline
模型 & AP & AP50 & AP75 & APm & APl \\
\hline
Cascade Mask R-CNN (2018) & 43.01 & 74.79 & 55.80 & 45.58 & 58.44 \\
Swin Transformer (2021) & 40.41 & 73.79 & 50.91 & 43.95 & 46.31 \\
Hybrid Task Cascade (2019) & 42.33 & 70.83 & 48.99 & 44.12 & 58.67 \\
YOLOv5 (2020) & 33.04 & 69.74 & 29.93 & 36.94 & 34.03 \\
YOLACT (2019) & 31.61 & 65.41 & 21.62 & 37.80 & 26.24 \\
QueryInst (2021) & 33.69 & 66.25 & 29.29 & 39.26 & 40.66 \\
Mask R-CNN (2017) & 34.83 & 68.41 & 36.24 & 41.70 & 29.61 \\
SOLOv2 (2020) & 27.42 & 60.81 & 20.97 & 31.04 & 40.19 \\
DetecTree2 (2023) & 43.41 & 68.54 & 51.18 & 49.63 & 58.37 \\
MASK\_RCNN\_TCDD (2022) & 43.37 & 67.53 & 50.60 & 47.86 & 59.18 \\
Tree Crown Delineator (2021) & 32.93 & 66.09 & 38.50 & 32.54 & 41.54 \\
\textbf{CSAF (Ours)} & \textbf{48.31} & \textbf{77.03} & \textbf{59.66} & \textbf{51.95} & \textbf{63.32} \\
\hline
\end{tabular}
\end{table*}

值得注意的是，专门为树冠分割设计的模型（如DetecTree2 (2023)、MASK\_RCNN\_TCDD (2022)和Tree Crown Delineator (2021)）在该数据集上表现不佳。特别是Tree Crown Delineator (2021)相比CSAF在AP上低15.38个百分点，突显了其对多样化冠层类型的适应性有限以及对小尺寸树冠的描绘能力不足。可视化分析（图\ref{fig:multi_dataset_visual}(e)-(f)）进一步证实了CSAF在小目标检测方面的优势，能够成功识别和准确分割极小的树冠，而其他模型在这些区域往往失效。此外，CSAF在处理非目标植被干扰方面表现出强鲁棒性，有效抑制了背景噪声的影响。

\subsection{在CTF-Set数据集上的泛化实验}

为进一步验证CSAF框架的跨域泛化能力，我们在CTF-Set数据集上进行了零样本泛化实验，直接应用在RT-Set上训练的模型权重，未进行任何额外微调。

CTF-Set数据集采集自加拿大魁北克省圣伊波利特（45°59'N, 74°00'W）的温带混交林，研究区域跨越丘陵、湿地和湖泊等多样地形，支持典型的北美未管理森林中的多种落叶和针叶树种。该数据集包含5,321张图像，专门用作测试集以评估模型的泛化性能。数据集的关键挑战在于冠层内的高物种多样性，包含12个植物科的24个异质树种，具有独特但经常重叠的冠层结构。

在此泛化实验中，我们直接应用在RT-Set上训练的模型权重，未进行任何额外微调，以严格评估模型的零样本泛化性能。该数据集的多样化树种和复杂的冠层形态为模型的泛化能力提出了极大挑战。

表\ref{tab:ctf_set_generalization}展示了在CTF-Set数据集上的泛化实验结果。CSAF在这一极具挑战性的测试中仍保持优越性能，AP达到35.43\%，AP50达到76.71\%，APl达到35.48\%，在所有评估指标上均显著优于对比方法。相比次优方法DetecTree2 (2023)，CSAF在AP上提升了3.88个百分点，在AP50上提升了7.04个百分点，充分证明了其强大的跨域适应能力。
\begin{table*}[htbp]
\centering
\caption{CTF-Set数据集上的泛化性能对比}
\label{tab:ctf_set_generalization}
\footnotesize
\begin{tabular}{l|cccc}
\hline
模型 & AP & AP50 & AP75 & APl \\
\hline
Cascade Mask R-CNN (2018) & 29.30 & 67.80 & 24.60 & 29.50 \\
Swin Transformer (2021) & 27.10 & 59.40 & 24.30 & 27.30 \\
Hybrid Task Cascade (2019) & 22.00 & 53.50 & 16.30 & 22.30 \\
YOLOv5 (2020) & 18.30 & 48.90 & 11.00 & 18.40 \\
YOLACT (2019) & 22.10 & 53.00 & 15.30 & 22.20 \\
QueryInst (2021) & 13.80 & 43.80 & 6.20 & 13.90 \\
Mask R-CNN (2017) & 30.20 & 63.90 & 24.10 & 30.10 \\
SOLOv2 (2020) & 11.40 & 39.60 & 3.10 & 11.40 \\
DetecTree2 (2023) & 31.55 & 69.67 & 28.77 & 31.54 \\
MASK\_RCNN\_TCDD (2022) & 25.65 & 65.68 & 24.03 & 28.94 \\
Tree Crown Delineator (2021) & 23.00 & 60.44 & 18.68 & 23.00 \\
\textbf{CSAF (Ours)} & \textbf{35.43} & \textbf{76.71} & \textbf{27.57} & \textbf{35.48} \\
\hline
\end{tabular}
\end{table*}

令人惊讶的是，CSAF在CTF-Set上的某些指标甚至超过了其在RT-Set上的表现，这一现象可能归因于混交林中树冠的形态和光谱异质性。与单一林分中通常观察到的均匀特征相比，混交林的异质性有助于模型区分个体冠层，从而提高分割精度。此外，MASA-Optimizer模块嵌入的持续学习机制使CSAF能够在训练过程中逐步适应多样化的冠层形态和光谱变异，产生更平滑的特征表示转换并增强泛化稳定性。相比之下，其他对比模型在该数据集上表现出显著的性能下降，整体结果远低于CSAF，进一步证实了CSAF在跨物种森林分割场景中的鲁棒性和广泛适应性。

值得注意的是，进一步的可视化分析（图\ref{fig:multi_dataset_visual}(g)-(h)）表明，在CTF-Set混交林泛化实验中，主流方法在面对物种多样性和复杂冠层结构时，普遍存在明显的漏分割和误分割现象。例如，DetecTree2 (2023)和MASK\_RCNN\_TCDD (2022)等模型在部分区域无法准确区分不同树种的冠层边界，导致多个个体被合并或遗漏。相比之下，CSAF即使在未见过的多物种环境下，依然能够保持清晰的边界识别和较高的分割完整性，充分体现了其跨域泛化能力和鲁棒性。

\section{讨论}

\subsection{树冠计数方法与精度分析}

为定量评估不同模型的计数性能，本研究采用基于准确率的计数指标，计算公式如下：

\begin{equation}
\text{Accuracy} = 1 - \frac{|\text{Predicted} - \text{Real}|}{\text{Real}}
\end{equation}

其中，Predicted表示模型预测的树冠数量，Real表示人工标注的真实树冠数量。该指标直观反映了模型在树冠计数任务中的准确性，数值越接近1表示计数精度越高。

\subsection{在单一林的计数实验}

在RT-Set数据集的复杂条件下，CSAF展现出明显的性能优势。该数据集具有更高的冠层密度、显著的物候变化、模糊的冠层边界以及大量非目标植被干扰。CSAF在AP50指标上相比DetecTree2 (2023)模型提升5.35\%，相比SOLOv2 (2020)模型提升超过30\%。特别值得注意的是，尽管落叶期样本仅占训练数据的4.7\%，CSAF仍能在落叶期准确分割冠层结构，而其他模型主要关注可见叶片区域，难以检测落叶条件下的冠层轮廓。计数实验结果显示，CSAF在不同季节的树木计数任务中表现出高稳定性，年平均精度达到91.16\%，显著高于DetecTree2 (2023)模型的63.75\%。可视化分析进一步证实了这一结论，CSAF即使在密集冠层区域也能准确描绘单株树冠边界并实现实例级分割，遗漏极少，而DetecTree2 (2023)模型经常在局部区域产生覆盖多个冠层的单一掩码，且无法检测大量树冠，严重影响计数精度。

\subsection{在混交林的对比实验}

在CTF-Set数据集的跨物种泛化实验中，我们直接应用在RT-Set上训练的模型权重，未进行任何额外微调。该数据集包含12个植物科的24个异质树种，冠层形态差异显著。实验结果表明，CSAF在应用于先前未见的混合物种森林场景时仍保持优越的分割和计数性能，甚至超过了其在RT-Set上的结果。我们认为，这一结果可能归因于混交林中树冠的形态和光谱异质性，有助于模型区分个体冠层，从而提高分割和计数精度。进一步的可视化分析（图\ref{fig:multi_dataset_visual}(g)-(h)）表明，主流方法在面对物种多样性和复杂冠层结构时，普遍存在明显的漏分割和误分割现象，而CSAF即使在未见过的多物种环境下，依然能够保持清晰的边界识别和较高的分割完整性，充分体现了其跨域泛化能力和鲁棒性。

\subsection{计数误差分析}

为确保冠层计数结果的生态可解释性和实用性，必须在保持有效控制分割误差的同时实现个体树冠的精确描绘，特别是在以多样化物种组成和物候阶段为特征的生态异质条件下。借鉴先前研究的既定误差分类方法，我们将常见的分割误差分为三种主要类型：(1)漏检冠层、(2)合并或破碎冠层、(3)错误植被分割。

在三个代表性数据集（RRT-Set、RA-Set和CTF-Set）中，我们观察到在不同生态和物候条件下分割误差类型分布的显著差异。在RRT-Set中，季节变化显著影响模型识别冠层边界的能力。9月和10月观察到的过分割和欠分割与冠层密度增加和冠层轮廓模糊密切相关。尽管CSAF和DT都受到季节变化的影响，但CSAF始终表现出更稳定的误差控制，特别是在处理合并和破碎冠层案例方面。这一优势主要归因于GM-Mamba模块的结构设计，该模块增强了长程空间依赖建模和边界描绘。在错误植被分割方面，两个模型之间的差异相对较小，这可能是由于干扰植被与目标冠层之间的光谱和形态相似性，限制了MPC-Poisson模块形态学正则化的有效性。

在RA-Set数据集中，非目标植被的结构不规则性为MPC-Poisson模块提供了更清晰的几何线索，使CSAF在抑制背景干扰方面优于基线模型DT。在CTF-Set数据集的跨物种评估中，分割误差的分布呈现出独特的模式。在这种异质森林环境中，模型误差主要源于对种间形态变异的适应不足以及对非冠层区域（如湖泊边缘或裸露岩石表面）的误分类。DT模型在Zone1和Zone2中经常将岩石构造误识别为树冠，而CSAF偶尔将图像边缘或水体误分类为植被。这些误分类反映了模型在此类条件下强泛化能力的潜在副作用。

\subsection{方法局限性与改进方向}

尽管CSAF在各种测试场景中表现出令人印象深刻的性能，但仍存在一些局限性。首先，尽管PINN的集成和基于扩散方程的形态学约束的使用显著提高了模型抑制草地等不规则形状植被的能力，但在区分和抑制表现出冠状结构的非目标植被方面仍存在困难。在这些情况下，模型的分割精度仍然不够理想。

其次，CSAF在密集植被区域实现个体树冠的一致长期监测方面遇到挑战。光照条件的变化和冠层形态的时间变化可能导致同一树冠在不同时间点的描绘不一致，从而导致分割结果的时间波动。解决这些挑战将是我们未来工作的关键目标。具体而言，我们计划开发一个更强大的框架，能够确保稳定的长期冠层级跟踪。此外，增强模型抑制形态相似的非目标植被的能力并进一步提高其整体分割精度将继续是我们持续研究工作的核心。

第三，虽然MASA-Optimizer模块通过持续学习机制改善了多物种环境中的跨域适应性，但其持续学习机制可能偶尔由于先前学习特征的不平衡保留和新特征的整合而导致错误的冠层识别。这一观察突出了需要结合对低置信度区域的显式抑制策略，以在不损害模型适应灵活性的情况下保持边界精度。

\section{结论}

森林生态系统日益受到生态干扰和极端气候事件的威胁，特别是在植被动态对季节波动和风暴相关影响高度敏感的热带地区。由于复杂的冠层结构和持续的时间变异性，准确监测此类森林仍然是一个重大挑战。本研究提出了冠层分割与分析框架（CSAF），这是一个模块化深度学习系统，集成了结构先验、自适应学习策略和物理约束机制，显著提高了单株树冠分割和分析的准确性和鲁棒性。

通过集成用于边界增强的GM-Mamba、用于持续学习的MASA-Optimizer和用于形态学正则化的MPC-Poisson模块，CSAF有效解决了现有实例分割方法在复杂森林环境中应用时的关键局限性。为了实现更可靠的冠层监测，本研究引入了冠层感知植被指数计算方法SCVI。通过抑制杂草和土壤等背景干扰，SCVI增强了光谱信号保真度，确保在复杂环境中也能保持一致的监测性能。

在四个生态异质数据集上进行的实证研究表明，CSAF在树冠分割和计数任务中均实现了优越的性能，同时在不同的生态和物候条件下保持稳定的泛化能力。该框架能够准确处理单一林分和混交林的复杂场景，并支持从区域到单株树木和像素级别的多尺度生态解释。研究结果表明，结构感知分割机制与冠层级光谱量化的集成建立了更具弹性和可扩展的森林监测解决方案。

本研究强调了通过集成遥感数据、任务特定结构先验和持续学习策略来解决现实生态系统中时间和结构变异性引入的不确定性的迫切需要。通过利用高分辨率无人机影像，所提出的系统为长期生态监测提供了一种新颖的方法，特别适用于观测代表性不足或正在经历快速转变的森林区域。未来的研究将重点关注增强模型在极端结构异质性区域的适应能力，开发更强大的长期跟踪机制，以及进一步提高非目标植被抑制的精度。

\section*{致谢}

感谢中央级公益性科研院所基本科研业务费专项资金（1630032022007）、海南省自然科学基金（324MS087）、国家重点研发计划（2019YFD1000500）、农业农村部橡胶树生物学与遗传资源利用重点实验室开放课题（RRI-KLOF202305）以及国家自然科学基金（32271877）的资助。感谢中国热带农业科学院橡胶研究所提供的实验场地和数据支持。

\bibliographystyle{IEEEtran}
\bibliography{ieee_tgrs_references}

\end{document}
